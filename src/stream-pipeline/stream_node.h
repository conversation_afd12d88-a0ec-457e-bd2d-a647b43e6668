#pragma once

#include "stream_link.h"
#include <functional>

namespace SPipeline {

/**
 * StreamNode - Abstract base class for passive stream processing components
 */
template<typename TInput, typename TOutput>
class StreamNode: public PipelineComponent {
public:
  ~StreamNode() override = default;

  /**
  * Connect this node to an input link
  */
  void connectInputLink(IStreamLinkOutput<TInput>* link) {
    if (link) {
      link->onOutput([this](TInput data) -> bool {
        return process(data);
      });
    }
  }

  /**
  * Connect this node to an output link
  */
  void connectOutputLink(IStreamLinkInput<TOutput>* link) {
    if (link) {
      // Forwarding node's output to the link, and returning the continue flag
      outputCallback_ = [link](TOutput data) -> bool {
        return link->forward(data);
      };
    } else {
      // If no output link is connected, set a no-op callback
      outputCallback_ = [](TOutput data) -> bool {
        return false;
      };
    }
  }

protected:
  /**
  * Process input data and produce output. Should return true to continue processing.
  * This method is called internally by the framework when data arrives through connected links.
  */
  virtual bool process(TInput input) = 0;

  /**
   * Send processed data to the connected output link
   */
  bool sendOutput(TOutput data) {
    return outputCallback_ ? outputCallback_(data) : false;
  }

private:
  std::function<bool(TOutput)> outputCallback_;
};

} // namespace SPipeline
