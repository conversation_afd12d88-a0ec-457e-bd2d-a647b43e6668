#include "../passthrough_link.h"
#include "../pipeline_component.h"
#include <atomic>
#include <iostream>

using namespace SPipeline;

// Test data structure
struct SimpleData {
    int value;
    SimpleData(int v) : value(v) {}
    SimpleData(const SimpleData& other) : value(other.value) {}
    SimpleData(SimpleData&& other) noexcept : value(other.value) {}
    SimpleData& operator=(const SimpleData& other) {
        value = other.value;
        return *this;
    }
    SimpleData& operator=(SimpleData&& other) noexcept {
        value = other.value;
        return *this;
    }
};

// Mock Tickable implementation for testing
class MockTickableComponent : public PipelineComponent {
private:
    std::atomic<bool> isShutdown_{false};
    std::atomic<int> tickCount_{0};
    bool hasWork_;

public:
    MockTickableComponent(bool hasWork = true) : hasWork_(hasWork) {}
    
    bool tick() override {
        if (isShutdown_.load()) {
            return false;
        }
        tickCount_++;
        return hasWork_;
    }
    
    bool hasPendingWork() const override {
        return hasWork_ && !isShutdown_.load();
    }
    
    void stop() override {
        isShutdown_.store(true);
    }
    
    // Test helpers
    int getTickCount() const { return tickCount_.load(); }
    bool isShutdown() const { return isShutdown_.load(); }
    void setHasWork(bool hasWork) { hasWork_ = hasWork; }
};

// Test utilities
class TickableInterfaceTestSuite {
private:
    int testsPassed = 0;
    int testsTotal = 0;
    
    void assert_test(bool condition, const std::string& testName) {
        testsTotal++;
        if (condition) {
            testsPassed++;
            std::cout << "✓ " << testName << std::endl;
        } else {
            std::cout << "✗ " << testName << " FAILED" << std::endl;
        }
    }

public:
    // Test 1: Basic Tickable interface
    void test_basic_tickable_interface() {
        std::cout << "\n--- Test 1: Basic Tickable Interface ---" << std::endl;
        
        MockTickableComponent component(true);
        
        // Test initial state
        assert_test(!component.isShutdown(), "Component not shutdown initially");
        assert_test(component.hasPendingWork(), "Component has pending work initially");
        
        // Test tick functionality
        bool tickResult1 = component.tick();
        assert_test(tickResult1, "First tick returns true");
        assert_test(component.getTickCount() == 1, "Tick count incremented");
        
        bool tickResult2 = component.tick();
        assert_test(tickResult2, "Second tick returns true");
        assert_test(component.getTickCount() == 2, "Tick count incremented again");
    }
    
    // Test 2: Shutdown functionality
    void test_shutdown_functionality() {
        std::cout << "\n--- Test 2: Shutdown Functionality ---" << std::endl;
        
        MockTickableComponent component(true);
        
        // Test before shutdown
        bool tickResult1 = component.tick();
        assert_test(tickResult1, "Tick returns true before shutdown");
        assert_test(component.hasPendingWork(), "Has pending work before shutdown");
        
        // Test shutdown
        component.stop();
        assert_test(component.isShutdown(), "Component is shutdown after calling shutdown()");
        assert_test(!component.hasPendingWork(), "No pending work after shutdown");
        
        // Test after shutdown
        bool tickResult2 = component.tick();
        assert_test(!tickResult2, "Tick returns false after shutdown");
    }
    
    // Test 3: Component with no work
    void test_no_work_component() {
        std::cout << "\n--- Test 3: Component With No Work ---" << std::endl;
        
        MockTickableComponent component(false); // No work to do
        
        assert_test(!component.hasPendingWork(), "Component has no pending work");
        
        bool tickResult = component.tick();
        assert_test(!tickResult, "Tick returns false when no work to do");
        assert_test(component.getTickCount() == 1, "Tick still called and counted");
    }
    
    // Test 4: PassthroughLink Tickable behavior
    void test_passthrough_link_tickable() {
        std::cout << "\n--- Test 4: PassthroughLink Tickable Behavior ---" << std::endl;
        
        PassthroughLink<SimpleData> link;
        
        // Test default tick behavior (should return false - no work)
        bool tickResult = link.tick();
        assert_test(!tickResult, "PassthroughLink tick returns false by default");
        
        // Test hasPendingWork (should return false - no buffering)
        bool hasPendingWork = link.hasPendingWork();
        assert_test(!hasPendingWork, "PassthroughLink has no pending work by default");
        
        // Test shutdown (should not crash)
        link.stop(); // Should be safe to call
        assert_test(true, "PassthroughLink shutdown completes without error");
    }
    
    // Test 5: Dynamic work state changes
    void test_dynamic_work_state() {
        std::cout << "\n--- Test 5: Dynamic Work State Changes ---" << std::endl;
        
        MockTickableComponent component(true);
        
        // Initially has work
        assert_test(component.hasPendingWork(), "Initially has pending work");
        bool tickResult1 = component.tick();
        assert_test(tickResult1, "Tick returns true when has work");
        
        // Change to no work
        component.setHasWork(false);
        assert_test(!component.hasPendingWork(), "No pending work after state change");
        bool tickResult2 = component.tick();
        assert_test(!tickResult2, "Tick returns false when no work");
        
        // Change back to has work
        component.setHasWork(true);
        assert_test(component.hasPendingWork(), "Has pending work after state change back");
        bool tickResult3 = component.tick();
        assert_test(tickResult3, "Tick returns true when work restored");
    }
    
    // Test 6: Multiple component coordination
    void test_multiple_component_coordination() {
        std::cout << "\n--- Test 6: Multiple Component Coordination ---" << std::endl;
        
        MockTickableComponent comp1(true);
        MockTickableComponent comp2(true);
        MockTickableComponent comp3(false);
        
        // Test all components before shutdown
        bool tick1 = comp1.tick();
        bool tick2 = comp2.tick();
        bool tick3 = comp3.tick();
        
        assert_test(tick1 && tick2, "Components with work return true");
        assert_test(!tick3, "Component without work returns false");
        
        // Shutdown one component
        comp1.stop();
        
        tick1 = comp1.tick();
        tick2 = comp2.tick();
        
        assert_test(!tick1, "Shutdown component returns false");
        assert_test(tick2, "Non-shutdown component still returns true");
        
        // Shutdown all
        comp2.stop();
        comp3.stop();
        
        tick1 = comp1.tick();
        tick2 = comp2.tick();
        tick3 = comp3.tick();
        
        assert_test(!tick1 && !tick2 && !tick3, "All shutdown components return false");
    }
    
    void run_all_tests() {
        std::cout << "=== Tickable Interface Test Suite ===" << std::endl;
        
        test_basic_tickable_interface();
        test_shutdown_functionality();
        test_no_work_component();
        test_passthrough_link_tickable();
        test_dynamic_work_state();
        test_multiple_component_coordination();
        
        std::cout << "\n=== Test Results ===" << std::endl;
        std::cout << "Passed: " << testsPassed << "/" << testsTotal << std::endl;
        
        if (testsPassed == testsTotal) {
            std::cout << "✓ All Tickable Interface tests PASSED!" << std::endl;
        } else {
            std::cout << "✗ Some Tickable Interface tests FAILED!" << std::endl;
        }
    }
    
    bool all_tests_passed() const {
        return testsPassed == testsTotal;
    }
};

// Function to be called from test runner
int run_tickable_interface_tests() {
    TickableInterfaceTestSuite suite;
    suite.run_all_tests();
    return suite.all_tests_passed() ? 0 : 1;
}
