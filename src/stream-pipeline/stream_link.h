#pragma once

#include "pipeline_component.h"
#include <functional>

namespace SPipeline {

/**
 * IStreamLinkInput - Interface for input links in the stream processing pipeline
 * Template Parameter:
 * @tparam TInput Input data type that this link receives
 */
template<typename TInput>
class IStreamLinkInput {
public:
  virtual ~IStreamLinkInput() = default;
  virtual bool forward(TInput data) = 0; // Forward input data through the link, returns true if successful
};

/**
 * IStreamLinkOutput - Interface for output links in the stream processing pipeline
 * Template Parameter:
 * @tparam TOutput Output data type that this link produces
 */
template<typename TOutput>
class IStreamLinkOutput {
public:
  using OutputCallback = std::function<bool(TOutput)>;

  virtual ~IStreamLinkOutput() = default;
  virtual void onOutput(OutputCallback callback) = 0; // Function to call with processed output data
};

/**
 * StreamLink - Template-based interface for connecting stream processing components
 */
template<typename TInput, typename TOutput>
class StreamLink : public PipelineComponent, public IStreamLinkInput<TInput>, public IStreamLinkOutput<TOutput> {

protected:
  typename IStreamLinkOutput<TOutput>::OutputCallback outputCallback_; // Callback to send output data
};

} // namespace SPipeline
