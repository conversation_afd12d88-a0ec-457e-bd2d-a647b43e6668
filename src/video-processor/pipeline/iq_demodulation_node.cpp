#include "./iq_demodulation_node.h"
#include <cmath>

namespace IQVideoProcessor::Pipeline {

// Helper for safe phase difference computation with edge handling
inline ComplexType phase_unwrap_diff(const std::complex<ComplexType>& a, const std::complex<ComplexType>& b) {
  // Arg difference between consecutive complex samples (b / a)
  // Using atan2 on imag/real of a*conj(b) or b*conj(a). We'll use a*conj(b) to get sign consistent.
  const auto c = a * std::conj(b);
  return std::atan2(c.imag(), c.real());
}

IQDemodulationNode::IQDemodulationNode() {
  // Initialize without allocation until first input defines sizes
  demodSegment_.totalSamples = 0;
  demodSegment_.effectiveSamples = 0;
  demodSegment_.leftOverlapSamples = 0;
  setRunning();
}

IQDemodulationNode::~IQDemodulationNode() {
  PipelineComponent::stop();
}

bool IQDemodulationNode::process(ComplexIQSegment* inSegment) {
  if (!inSegment || !running()) return false;

  // Ensure we don't exceed pre-allocated capacity
  const auto totalSamples = inSegment->totalSamples;
  // Taking 3 samples as a minimum for demodulation
  if (totalSamples <= 3) return false; // Edge policy: leave first and last sample untouched

  if (!demodSegment_.totalSamples) {
    // As soon as we receive the first window, allocate the buffer
    demodSegment_.data.resize(totalSamples);
    demodSegment_.totalSamples = totalSamples;
    demodSegment_.effectiveSamples = inSegment->effectiveSamples;
    demodSegment_.leftOverlapSamples = inSegment->leftOverlapSamples;
  }

  // Core demodulation: 1:1 sample ratio using instantaneous phase difference
  // y[i] = angle(x[i] * conj(x[i-1])) for i in [1, n-2]
  for (size_t i = 1; i + 1 < totalSamples; ++i) {
    demodSegment_.data[i] = phase_unwrap_diff(inSegment->data[i], inSegment->data[i-1]);
  }

  // Edge handling: copy first and last samples to avoid uninitialized values
  demodSegment_.data[0] = demodSegment_.data[1];
  demodSegment_.data[totalSamples-1] = demodSegment_.data[totalSamples-2];

  // Forward pointer to internal buffer (no allocations, no copies)
  if (!this->sendOutput(&demodSegment_)) {
    stop();
    return false;
  }
  return running();
}

} // namespace IQVideoProcessor::Pipeline

