#pragma once

#include "./iq_acquisition_node_types.h"
#include "../../stream-pipeline/async_bridge.h"

namespace IQVideoProcessor::Pipeline {

class IQAsyncBridge final : public SPipeline::AsyncBridge<ComplexIQSegment*> {
public:
  explicit IQAsyncBridge(const size_t maxBufferSize, const std::chrono::milliseconds tickTimeout = std::chrono::milliseconds(100)) : AsyncBridge(maxBufferSize, tickTimeout) {
    // Nothing to do here, just call base constructor
  }


};

} // namespace IQVideoProcessor::Pipeline
