#pragma once

#include "./iq_acquisition_node_types.h"
#include "./iq_demodulation_node_types.h"
#include "../../stream-pipeline/stream_node.h"

namespace IQVideoProcessor::Pipeline {

// Simple frequency demodulation node: input ComplexIQWindow*, output DemodulatedWindow*
class IQDemodulationNode final : public SPipeline::StreamNode<ComplexIQSegment*, DemodulatedSegment*> {
public:
  // Constructor defers allocation until first window defines sizes
  IQDemodulationNode();
  ~IQDemodulationNode() override;

private:
  bool process(ComplexIQSegment* inSegment) override;

  // Reusable output storage
  DemodulatedSegment demodSegment_;
};

} // namespace IQVideoProcessor::Pipeline

