#pragma once

#include "./iq_acquisition_node_types.h"
#include "../../chunk-processor/chunk_processor.h"
#include "../../iiq-stream/iiq_stream.h"
#include "../../stream-pipeline/stream_node.h"
#include "../../types.h"

namespace IQVideoProcessor::Pipeline {

class IQAcquisitionNode final : public SPipeline::StreamNode<bool, ComplexIQSegment&> {
public:
  explicit IQAcquisitionNode(
    std::unique_ptr<IIQStream> stream,
    size_t iqStreamReadSize,            // Number of samples to read from the IQ stream per single operation
    size_t effectiveSamplesPerSegment,  // Desired number of processable samples in the center region
    size_t leftOverlapSamples           // Number of overlap samples on the left (right assumed equal)
  );
  ~IQAcquisitionNode() override;

  bool tick() override;
  [[nodiscard]] bool hasPendingWork() const override;

private:
  size_t iqStreamReadSize_;
  size_t effectiveSamplesPerSegment_;
  size_t leftOverlapSamples_;
  std::unique_ptr<IIQStream> iqStream_;
  std::unique_ptr<ChunkProcessor<SampleType>> chunkProcessor_;

  ComplexIQSegment currentSegment_;

  bool process(bool input) override;
  void onOutputChunkReady(const SampleType *chunkBuffer, size_t chunkSize);
};

} // namespace IQVideoProcessor::Pipeline
